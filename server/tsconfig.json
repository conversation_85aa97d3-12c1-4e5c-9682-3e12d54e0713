{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": "./", "typeRoots": ["./node_modules/@types", "./types"], "paths": {"@/*": ["../src/services/endpoints/*"]}}, "include": ["server-ts.ts", "simple-production-server.ts", "standalone-enhanced-server.ts", "standalone-enhanced-server-production.ts", "enhanced-production-server.ts", "simple-email-server.ts", "monitoring-service.ts", "routes/**/*.ts", "types/**/*.ts", "src/types/*.ts", "middleware/**/*.ts"], "exclude": ["node_modules", "dist", "../node_modules", "../dist", "../build", "../src/components", "../src/hooks", "../src/lib", "../src/utils", "../src/pages", "../src/services", "**/*.test.ts", "**/*.spec.ts"]}