{"environments": {"production": {"api_port": 3001, "ws_port": 3002, "service_name": "investra-email-api-prod", "server_dir": "/opt/investra/email-api/production", "pm2_instances": 2, "deployment_mode": "systemd", "log_level": "warn", "nginx_server_name": "api.investra.com", "ssl_enabled": true, "monitoring_enabled": true, "backup_retention_days": 30}, "staging": {"api_port": 3002, "ws_port": 3003, "service_name": "investra-email-api-staging", "server_dir": "/opt/investra/email-api/staging", "pm2_instances": 1, "deployment_mode": "systemd", "log_level": "info", "nginx_server_name": "api-staging.investra.com", "ssl_enabled": false, "monitoring_enabled": true, "backup_retention_days": 7}, "development": {"api_port": 3003, "ws_port": 3004, "service_name": "investra-email-api-dev", "server_dir": "/opt/investra/email-api/development", "pm2_instances": 1, "deployment_mode": "pm2", "log_level": "debug", "nginx_server_name": "api-dev.investra.com", "ssl_enabled": false, "monitoring_enabled": false, "backup_retention_days": 3}}, "shared_config": {"log_dir": "/var/log/investra", "backup_dir": "/opt/investra/backups", "service_user": "investra", "service_group": "investra", "node_path": "/usr/bin/node", "npm_path": "/usr/bin/npm", "health_check_timeout": 30, "health_check_interval": 3, "restart_delay": 15, "max_restarts": 10, "memory_limit": "1G", "cpu_limit": "200%"}, "required_secrets": ["SUPABASE_URL", "SUPABASE_ANON_KEY", "EMAIL_HOST", "EMAIL_PORT", "EMAIL_USER", "EMAIL_PASSWORD", "IMAP_HOST", "IMAP_PORT", "IMAP_USER", "IMAP_PASSWORD"], "optional_secrets": ["SUPABASE_SERVICE_KEY", "DATABASE_URL", "LOG_LEVEL", "WS_ENABLED", "MONITORING_ENABLED"], "default_values": {"EMAIL_HOST": "localhost", "EMAIL_PORT": "587", "EMAIL_SECURE": "true", "IMAP_HOST": "localhost", "IMAP_PORT": "993", "IMAP_SECURE": "true", "IMAP_ENABLED": "true", "WS_ENABLED": "false", "MONITORING_ENABLED": "true"}, "port_conflicts": {"check_ports": [3001, 3002, 3003, 3004], "conflict_resolution": {"stop_services": ["investra-email-server", "investra-email-api-prod", "investra-email-api-staging", "investra-email-api-dev"], "kill_pm2_processes": true, "use_fuser": true, "wait_time": 3}}, "nginx_config": {"template_path": "/etc/nginx/conf.d", "upstream_name_template": "investra-api-{environment}", "server_name_template": "api-{environment}.investra.com", "proxy_timeout": 60, "enable_gzip": true, "enable_rate_limiting": true, "rate_limit": "100r/m"}, "systemd_config": {"service_template": "/etc/systemd/system/{service_name}.service", "restart_policy": "on-failure", "restart_sec": 15, "timeout_stop_sec": 30, "kill_mode": "mixed", "kill_signal": "SIGTERM", "security_settings": {"no_new_privileges": true, "private_tmp": true, "protect_system": "strict", "protect_home": true}}, "pm2_config": {"exec_mode": "cluster", "max_memory_restart": "1G", "min_uptime": "10s", "max_restarts": 10, "restart_delay": 4000, "kill_timeout": 5000, "listen_timeout": 3000, "merge_logs": true, "autorestart": true}, "health_checks": {"endpoints": ["/health", "/api/health", "/status"], "expected_responses": ["healthy", "ok", "running"], "timeout": 10, "retries": 3, "retry_delay": 2}, "monitoring": {"log_rotation": {"enabled": true, "max_size": "100M", "max_files": 10, "compress": true}, "metrics": {"enabled": true, "port": 9090, "path": "/metrics"}, "alerts": {"enabled": false, "webhook_url": "", "memory_threshold": "80%", "cpu_threshold": "80%", "disk_threshold": "90%"}}}