name: Deploy Email API Server (Self-Hosted)

on:
  push:
    branches: [ main, master, develop, staging ]
    paths:
      - 'server/**'
      - '.github/workflows/deploy-email-api.yml'
  pull_request:
    branches: [ main, master ]
    paths:
      - 'server/**'
      - '.github/workflows/deploy-email-api.yml'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'development'
        type: choice
        options:
          - development
          - staging
          - production

env:
  API_PORT: 3001
  SERVICE_NAME: investra-email-api
  SERVER_DIR: /opt/investra/email-api

jobs:
  determine-environment:
    name: Determine Environment
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.env.outputs.environment }}
      api-port: ${{ steps.env.outputs.api-port }}
      service-name: ${{ steps.env.outputs.service-name }}
      server-dir: ${{ steps.env.outputs.server-dir }}
      pm2-instances: ${{ steps.env.outputs.pm2-instances }}
    
    steps:
    - name: Determine environment and configuration
      id: env
      run: |
        if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
          ENVIRONMENT="${{ github.event.inputs.environment }}"
        elif [ "${{ github.event_name }}" = "pull_request" ]; then
          ENVIRONMENT="staging"
        else
          case "${{ github.ref_name }}" in
            main|master) ENVIRONMENT="production" ;;
            staging) ENVIRONMENT="staging" ;;
            develop|development) ENVIRONMENT="development" ;;
            *) ENVIRONMENT="development" ;;
          esac
        fi
        
        echo "environment=$ENVIRONMENT" >> $GITHUB_OUTPUT
        
        case "$ENVIRONMENT" in
          production)
            echo "api-port=3001" >> $GITHUB_OUTPUT
            echo "service-name=investra-email-api-prod" >> $GITHUB_OUTPUT
            echo "server-dir=/opt/investra/email-api-prod" >> $GITHUB_OUTPUT
            echo "pm2-instances=2" >> $GITHUB_OUTPUT
            ;;
          staging)
            echo "api-port=3002" >> $GITHUB_OUTPUT
            echo "service-name=investra-email-api-staging" >> $GITHUB_OUTPUT
            echo "server-dir=/opt/investra/email-api-staging" >> $GITHUB_OUTPUT
            echo "pm2-instances=1" >> $GITHUB_OUTPUT
            ;;
          development)
            echo "api-port=3003" >> $GITHUB_OUTPUT
            echo "service-name=investra-email-api-dev" >> $GITHUB_OUTPUT
            echo "server-dir=/opt/investra/email-api-dev" >> $GITHUB_OUTPUT
            echo "pm2-instances=1" >> $GITHUB_OUTPUT
            ;;
        esac

  deploy-email-api:
    name: Deploy Email API Server
    runs-on: self-hosted
    needs: determine-environment
    environment: ${{ needs.determine-environment.outputs.environment }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Debug environment
      run: |
        echo "Environment: ${{ needs.determine-environment.outputs.environment }}"
        echo "API Port: ${{ needs.determine-environment.outputs.api-port }}"
        echo "Service Name: ${{ needs.determine-environment.outputs.service-name }}"
        echo "Server Directory: ${{ needs.determine-environment.outputs.server-dir }}"
        echo "Current user: $(whoami)"
        echo "Server IP: $(hostname -I | awk '{print $1}')"

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: 'server/package-lock.json'

    - name: Prepare for deployment
      run: |
        echo "📋 Preparing for API server deployment"
        
        # Create deployment working directory
        mkdir -p ~/investra-email-api-deployment
        
        # Copy server files from the correct location (workspace root)
        cp -r server/* ~/investra-email-api-deployment/
        
        echo "✅ Deployment preparation complete"

    - name: Prepare deployment
      run: |
        cd ~/investra-email-api-deployment
        
        # Make deployment script executable
        chmod +x deploy-api-server.sh
        
        # Validate critical environment variables before deployment
        echo "🔍 Validating GitHub secrets..."
        
        # Check if secrets are configured or if we'll use defaults
        if [[ -n "${{ secrets.SUPABASE_URL }}" && -n "${{ secrets.SUPABASE_ANON_KEY }}" ]]; then
          echo "✅ GitHub secrets are configured - using repository secrets"
          echo "   SUPABASE_URL: ${{ secrets.SUPABASE_URL }}"
          echo "   SUPABASE_ANON_KEY: configured (truncated)"
        else
          echo "⚠️  GitHub secrets missing - using default values for testing"
          echo "   SUPABASE_URL: https://ecbuwhpipphdssqjwgfm.supabase.co (default)"
          echo "   SUPABASE_ANON_KEY: configured (default)"
          echo ""
          echo "🔧 For production, add these secrets in GitHub Settings → Secrets and variables → Actions:"
          echo "   - SUPABASE_URL"
          echo "   - SUPABASE_ANON_KEY"
          echo "📖 See GITHUB_SECRETS_CONFIGURATION.md for detailed instructions"
        fi
        
        echo "✅ Environment configuration ready - deployment will proceed"

    - name: Deploy application with environment variables
      run: |
        cd ~/investra-email-api-deployment
        
        echo "🚀 Starting deployment with comprehensive environment configuration..."
        
        # Create environment file for the deployment script
        cat > .deployment-env << EOF
        export ENVIRONMENT="${{ needs.determine-environment.outputs.environment }}"
        export SERVICE_NAME="${{ needs.determine-environment.outputs.service-name }}"
        export API_PORT="${{ needs.determine-environment.outputs.api-port }}"
        export SERVER_DIR="${{ needs.determine-environment.outputs.server-dir }}"
        export EMAIL_HOST="${{ secrets.EMAIL_HOST || 'localhost' }}"
        export EMAIL_PORT="${{ secrets.EMAIL_PORT || '587' }}"
        export EMAIL_USER="${{ secrets.EMAIL_USER || '<EMAIL>' }}"
        export EMAIL_PASSWORD="${{ secrets.EMAIL_PASSWORD || 'placeholder' }}"
        export IMAP_HOST="${{ secrets.IMAP_HOST || 'localhost' }}"
        export IMAP_PORT="${{ secrets.IMAP_PORT || '993' }}"
        export IMAP_USER="${{ secrets.IMAP_USER || '<EMAIL>' }}"
        export IMAP_PASSWORD="${{ secrets.IMAP_PASSWORD || 'placeholder' }}"
        export IMAP_SECURE="${{ secrets.IMAP_SECURE || 'true' }}"
        export IMAP_ENABLED="${{ secrets.IMAP_ENABLED || 'true' }}"
        export DATABASE_URL="${{ secrets.DATABASE_URL || 'postgresql://localhost:5432/investra' }}"
        export SUPABASE_URL="${{ secrets.SUPABASE_URL || 'https://ecbuwhpipphdssqjwgfm.supabase.co' }}"
        export SUPABASE_ANON_KEY="${{ secrets.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVjYnV3aHBpcHBoZHNycWp3Z2ZtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4NzU4NjEsImV4cCI6MjA2NDQ1MTg2MX0.QMWhB6lpgO3YRGg5kGKz7347DZzRcDiQ6QLupznZi1E' }}"
        export SUPABASE_SERVICE_KEY="${{ secrets.SUPABASE_SERVICE_KEY || 'placeholder' }}"
        export VITE_SUPABASE_URL="${{ secrets.SUPABASE_URL || 'https://ecbuwhpipphdssqjwgfm.supabase.co' }}"
        export VITE_SUPABASE_ANON_KEY="${{ secrets.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVjYnV3aHBpcHBoZHNycWp3Z2ZtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4NzU4NjEsImV4cCI6MjA2NDQ1MTg2MX0.QMWhB6lpgO3YRGg5kGKz7347DZzRcDiQ6QLupznZi1E' }}"
        export NODE_ENV="${{ needs.determine-environment.outputs.environment }}"
        export LOG_LEVEL="${{ secrets.LOG_LEVEL || 'info' }}"
        EOF
        
        echo "✅ Environment variables exported to .deployment-env"
        echo "🔍 Verifying critical variables:"
        source .deployment-env
        echo "   SUPABASE_URL: ${SUPABASE_URL}"
        echo "   SUPABASE_ANON_KEY: ${SUPABASE_ANON_KEY:0:20}... (truncated)"
        
        # Run deployment with environment variables from the file
        set -a
        source .deployment-env
        set +a
        
        ./deploy-api-server.sh deploy

    - name: Fix PM2 configuration
      run: |
        cd ~/investra-email-api-deployment
        
        echo "🔧 Preparing corrected PM2 ecosystem configuration..."
        
        # Remove any existing PM2 config files to ensure clean deployment
        rm -f ecosystem.*.config.js ecosystem.config.js
        
        # Create a corrected PM2 configuration template that the deploy script will use
        # This ensures the configuration uses the correct script path and environment settings
        
        cat > pm2-template.js << 'EOF'
        // Template for PM2 configuration
        // This will be used by the deployment script to generate the correct config
        module.exports = {
          apps: [
            {
              name: '${SERVICE_NAME}',
              script: 'dist/standalone-enhanced-server-production.js',  // Production standalone server
              cwd: '${SERVER_DIR}',
              instances: '${PM2_INSTANCES}',
              exec_mode: 'cluster',
              
              env: {
                NODE_ENV: '${ENVIRONMENT}',
                PORT: '${API_PORT}',
                LOG_LEVEL: 'info'
              },
              
              max_memory_restart: '1G',
              min_uptime: '10s',
              max_restarts: 10,
              restart_delay: 4000,
              
              log_file: '/var/log/investra/email-api-combined.log',
              out_file: '/var/log/investra/email-api-out.log',
              error_file: '/var/log/investra/email-api-error.log',
              log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
              merge_logs: true,
              
              watch: false,
              autorestart: true,
              vizion: false,
              
              node_args: [
                '--max-old-space-size=2048',
                '--enable-source-maps'
              ],
              
              env_file: '.env.${ENVIRONMENT}',
              kill_timeout: 5000,
              listen_timeout: 3000,
              treekill: true
            }
          ]
        };
        EOF
        
        echo "✅ PM2 configuration template prepared"
        echo "📁 The deployment script will handle creating the final configuration"

    - name: Ensure PM2 starts with correct environment
      run: |
        cd ~/investra-email-api-deployment
        
        echo "🔄 Ensuring PM2 service starts with correct environment variables..."
        
        # Get the service name and server directory
        SERVICE_NAME="${{ needs.determine-environment.outputs.service-name }}"
        SERVER_DIR="${{ needs.determine-environment.outputs.server-dir }}"
        
        # Navigate to the deployment directory
        cd "$SERVER_DIR" || exit 1
        
        # Stop any existing processes to ensure clean restart
        pm2 stop "$SERVICE_NAME" 2>/dev/null || true
        pm2 delete "$SERVICE_NAME" 2>/dev/null || true
        
        # Start with explicit environment variables to ensure Supabase config is loaded
        SUPABASE_URL="${{ secrets.SUPABASE_URL }}" \
        SUPABASE_ANON_KEY="${{ secrets.SUPABASE_ANON_KEY }}" \
        VITE_SUPABASE_URL="${{ secrets.SUPABASE_URL }}" \
        VITE_SUPABASE_ANON_KEY="${{ secrets.SUPABASE_ANON_KEY }}" \
        pm2 start "ecosystem.${{ needs.determine-environment.outputs.environment }}.config.js" --update-env
        
        # Save the PM2 configuration
        pm2 save
        
        # Wait for startup and verify
        sleep 10
        if pm2 list | grep -q "$SERVICE_NAME.*online"; then
          echo "✅ PM2 service started successfully with environment variables"
        else
          echo "❌ PM2 service failed to start"
          pm2 logs "$SERVICE_NAME" --lines 20
          exit 1
        fi

    - name: Verify deployment
      run: |
        cd ~/investra-email-api-deployment
        
        echo "🔍 Running comprehensive deployment verification..."
        
        # Make the verification script executable
        chmod +x check-pm2-deployment.sh
        
        # Run the deployment verification script
        ./check-pm2-deployment.sh "${{ needs.determine-environment.outputs.environment }}"
        
        # Additional verification specific to our fix
        echo ""
        echo "🔍 Verifying Supabase environment configuration:"
        
        # Test the specific endpoint that was failing
        API_PORT="${{ needs.determine-environment.outputs.api-port }}"
        TEST_ENDPOINT="http://localhost:$API_PORT/api/manual-review/stats"
        
        echo "Testing endpoint: $TEST_ENDPOINT"
        RESPONSE=$(curl -s "$TEST_ENDPOINT" || echo '{"error":"connection_failed"}')
        
        # Check if we get the expected authentication error (not a 500 error)
        if echo "$RESPONSE" | grep -q "Missing or invalid Authorization header"; then
          echo "✅ Endpoint responding correctly (authentication error expected)"
        elif echo "$RESPONSE" | grep -q "Authentication service not configured"; then
          echo "❌ Supabase configuration error detected"
          exit 1
        elif echo "$RESPONSE" | grep -q "relation.*does not exist"; then
          echo "❌ Database migration error detected"
          exit 1
        elif echo "$RESPONSE" | grep -q "connection_failed"; then
          echo "❌ API server not responding"
          exit 1
        else
          echo "⚠️ Unexpected response: $RESPONSE"
          echo "ℹ️ This may be normal depending on endpoint behavior"
        fi
        
        # Check Nginx proxy if configured
        if command -v nginx &> /dev/null && systemctl is-active --quiet nginx; then
          if curl -f http://localhost/health &>/dev/null; then
            echo "✅ Nginx proxy working"
          else
            echo "⚠️ Nginx proxy not responding"
          fi
        fi
        
        echo "🎉 Deployment verification complete!"
